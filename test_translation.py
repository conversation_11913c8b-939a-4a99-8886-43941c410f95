#!/usr/bin/env python3
"""
Test script for Pokemon translation functionality
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from plugins.pokemon.database import PokemonDatabase, translation_manager


async def test_translation_manager():
    """Test the translation manager"""
    print("Testing Translation Manager...")
    
    # Initialize translation manager
    success = await translation_manager.initialize()
    if not success:
        print("❌ Failed to initialize translation manager")
        return False
    
    print("✅ Translation manager initialized successfully")
    
    # Test Chinese to English translation
    test_cases = [
        ("皮卡丘", "Pikachu"),
        ("火焰拳", "Fire Punch"),
        ("十万伏特", "Thunderbolt"),
        ("电击", "Thunder Shock"),
    ]
    
    print("\nTesting Chinese to English translation:")
    for chinese, expected_english in test_cases:
        result = translation_manager.translate_to_english(chinese)
        if result:
            print(f"✅ {chinese} -> {result}")
            if expected_english.lower() in result.lower():
                print(f"   ✅ Matches expected: {expected_english}")
            else:
                print(f"   ⚠️  Expected: {expected_english}, Got: {result}")
        else:
            print(f"❌ {chinese} -> No translation found")
    
    # Test English to Chinese translation
    print("\nTesting English to Chinese translation:")
    english_test_cases = ["Pikachu", "Fire Punch", "Thunderbolt", "Thunder Shock"]
    for english in english_test_cases:
        result = translation_manager.translate_to_chinese(english)
        if result:
            print(f"✅ {english} -> {result}")
        else:
            print(f"❌ {english} -> No translation found")
    
    return True


async def test_pokemon_database():
    """Test the Pokemon database with Chinese names"""
    print("\n" + "="*50)
    print("Testing Pokemon Database with Chinese Names...")
    
    # Initialize database
    db = PokemonDatabase()
    success = await db.initialize()
    if not success:
        print("❌ Failed to initialize Pokemon database")
        return False
    
    print("✅ Pokemon database initialized successfully")
    
    # Test getting Pokemon by Chinese name
    print("\nTesting get_pokemon with Chinese names:")
    chinese_pokemon_names = ["皮卡丘", "雷丘", "妙蛙种子", "小火龙"]
    
    for chinese_name in chinese_pokemon_names:
        pokemon = await db.get_pokemon(chinese_name)
        if pokemon:
            print(f"✅ Found {chinese_name}: {pokemon.name} (#{pokemon.num:03d})")
        else:
            print(f"❌ Could not find Pokemon: {chinese_name}")
    
    # Test searching Pokemon by Chinese name
    print("\nTesting search_pokemon with Chinese names:")
    search_queries = ["皮卡", "火", "水", "草"]
    
    for query in search_queries:
        results = await db.search_pokemon(query, limit=3)
        if results:
            print(f"✅ Search '{query}' found {len(results)} results:")
            for pokemon in results:
                chinese_name = translation_manager.translate_to_chinese(pokemon.name)
                print(f"   - {pokemon.name} ({chinese_name or 'No Chinese name'})")
        else:
            print(f"❌ Search '{query}' found no results")
    
    return True


async def test_moves_and_items():
    """Test moves and items with Chinese names"""
    print("\n" + "="*50)
    print("Testing Moves and Items with Chinese Names...")
    
    db = PokemonDatabase()
    await db.initialize()
    
    # Test moves
    print("\nTesting get_move with Chinese names:")
    chinese_move_names = ["十万伏特", "火焰拳", "冰冻拳", "雷电拳"]
    
    for chinese_name in chinese_move_names:
        move = await db.get_move(chinese_name)
        if move:
            print(f"✅ Found move {chinese_name}: {move.name} (Power: {move.base_power})")
        else:
            print(f"❌ Could not find move: {chinese_name}")
    
    # Test items
    print("\nTesting get_item with Chinese names:")
    chinese_item_names = ["大师球", "超级球", "高级球", "治疗药"]
    
    for chinese_name in chinese_item_names:
        item = await db.get_item(chinese_name)
        if item:
            print(f"✅ Found item {chinese_name}: {item.name}")
        else:
            print(f"❌ Could not find item: {chinese_name}")
    
    return True


async def main():
    """Main test function"""
    print("Pokemon Translation System Test")
    print("="*50)
    
    try:
        # Test translation manager
        success1 = await test_translation_manager()
        
        # Test Pokemon database
        success2 = await test_pokemon_database()
        
        # Test moves and items
        success3 = await test_moves_and_items()
        
        print("\n" + "="*50)
        if success1 and success2 and success3:
            print("✅ All tests completed successfully!")
            print("🎉 Chinese name translation is working!")
        else:
            print("❌ Some tests failed. Please check the output above.")
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
