#!/usr/bin/env python3
"""
Pokemon Plugin Test Script

Basic test script to verify the Pokemon plugin functionality.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

from nonebot import logger


async def test_database():
    """Test Pokemon database functionality"""
    logger.info("Testing Pokemon database...")
    
    try:
        from src.plugins.pokemon.database import PokemonDatabase
        
        db = PokemonDatabase()
        success = await db.initialize()
        
        if not success:
            logger.error("Failed to initialize database")
            return False
        
        # Test getting Pokemon
        pikachu = await db.get_pokemon("pikachu")
        if pikachu:
            logger.info(f"Found Pokemon: {pikachu.name} (#{pikachu.num})")
            logger.info(f"Types: {[t.value for t in pikachu.types]}")
            logger.info(f"Base stats total: {sum([
                pikachu.base_stats.hp,
                pikachu.base_stats.attack,
                pikachu.base_stats.defense,
                pikachu.base_stats.special_attack,
                pikachu.base_stats.special_defense,
                pikachu.base_stats.speed
            ])}")
        else:
            logger.warning("Could not find Pikachu")
        
        # Test getting move
        thunderbolt = await db.get_move("thunderbolt")
        if thunderbolt:
            logger.info(f"Found move: {thunderbolt.name}")
            logger.info(f"Type: {thunderbolt.type.value}, Power: {thunderbolt.base_power}")
        else:
            logger.warning("Could not find Thunderbolt")
        
        # Test search
        electric_pokemon = await db.search_pokemon("", limit=5, filters={"type": "Electric"})
        logger.info(f"Found {len(electric_pokemon)} Electric Pokemon")
        
        # Get database stats
        stats = await db.get_database_stats()
        logger.info(f"Database stats: {stats}")
        
        logger.info("Database test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Database test failed: {e}")
        return False


async def test_showdown_bridge():
    """Test Pokemon Showdown bridge functionality"""
    logger.info("Testing Pokemon Showdown bridge...")
    
    try:
        from src.plugins.pokemon.showdown_bridge import ShowdownBridge
        
        bridge = ShowdownBridge()
        success = await bridge.initialize()
        
        if not success:
            logger.error("Failed to initialize Showdown bridge")
            return False
        
        # Test health check
        health = await bridge.health_check()
        logger.info(f"Bridge health: {health}")
        
        # Test format info
        from src.plugins.pokemon.models import BattleFormat
        format_info = await bridge.get_format_info(BattleFormat.GEN9OU)
        if format_info:
            logger.info(f"Format info for gen9ou: {format_info}")
        
        logger.info("Showdown bridge test completed successfully")
        await bridge.shutdown()
        return True
        
    except Exception as e:
        logger.error(f"Showdown bridge test failed: {e}")
        return False


async def test_team_validation():
    """Test team validation functionality"""
    logger.info("Testing team validation...")
    
    try:
        from src.plugins.pokemon.database import PokemonDatabase
        from src.plugins.pokemon.showdown_bridge import ShowdownBridge
        from src.plugins.pokemon.battle import TeamValidator
        from src.plugins.pokemon.models import Team, PokemonSet, BattleFormat
        
        # Initialize components
        db = PokemonDatabase()
        await db.initialize()
        
        bridge = ShowdownBridge()
        await bridge.initialize()
        
        validator = TeamValidator(db, bridge)
        
        # Create a test team
        test_team = Team(
            name="Test Team",
            pokemon=[
                PokemonSet(
                    name="Pikachu",
                    species="Pikachu",
                    moves=["Thunderbolt", "Quick Attack", "Iron Tail", "Agility"],
                    level=50
                ),
                PokemonSet(
                    name="Charizard",
                    species="Charizard",
                    moves=["Flamethrower", "Dragon Claw", "Solar Beam", "Roost"],
                    level=50
                )
            ],
            format=BattleFormat.GEN9OU,
            owner_id="test"
        )
        
        # Validate team
        is_valid, errors = await validator.validate_team(test_team, BattleFormat.GEN9OU)
        
        if is_valid:
            logger.info("Test team is valid!")
        else:
            logger.info(f"Test team validation errors: {errors}")
        
        logger.info("Team validation test completed")
        await bridge.shutdown()
        return True
        
    except Exception as e:
        logger.error(f"Team validation test failed: {e}")
        return False


async def test_battle_manager():
    """Test battle manager functionality"""
    logger.info("Testing battle manager...")
    
    try:
        from src.plugins.pokemon.database import PokemonDatabase
        from src.plugins.pokemon.showdown_bridge import ShowdownBridge
        from src.plugins.pokemon.battle import BattleManager
        from src.plugins.pokemon.models import Team, PokemonSet, BattleFormat
        
        # Initialize components
        db = PokemonDatabase()
        await db.initialize()
        
        bridge = ShowdownBridge()
        await bridge.initialize()
        
        manager = BattleManager()
        await manager.initialize(db, bridge)
        
        # Get battle stats
        stats = await manager.get_battle_stats()
        logger.info(f"Battle manager stats: {stats}")
        
        logger.info("Battle manager test completed")
        await manager.shutdown()
        await bridge.shutdown()
        return True
        
    except Exception as e:
        logger.error(f"Battle manager test failed: {e}")
        return False


async def main():
    """Run all tests"""
    logger.info("Starting Pokemon Plugin tests...")
    
    tests = [
        ("Database", test_database),
        ("Showdown Bridge", test_showdown_bridge),
        ("Team Validation", test_team_validation),
        ("Battle Manager", test_battle_manager),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running {test_name} test...")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{test_name} test: {status}")
        except Exception as e:
            logger.error(f"{test_name} test error: {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Pokemon Plugin is ready to use.")
        return 0
    else:
        logger.error("❌ Some tests failed. Please check the logs above.")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test runner error: {e}")
        sys.exit(1)
