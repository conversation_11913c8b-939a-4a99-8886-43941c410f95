#!/usr/bin/env python3
"""
Test Enhanced Translation System

Tests the improved translation manager with context awareness and special cases.
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.plugins.pokemon.database.translation_manager import (
    translation_manager, 
    display_translator, 
    TranslationContext
)


async def test_basic_translation():
    """Test basic translation functionality"""
    print("=== Testing Basic Translation ===")
    
    # Initialize translation manager
    success = await translation_manager.initialize()
    if not success:
        print("❌ Failed to initialize translation manager")
        return False
    
    print("✅ Translation manager initialized successfully")
    
    # Test basic translations
    test_cases = [
        ("皮卡丘", "Pikachu", TranslationContext.POKEMON),
        ("火焰拳", "Fire Punch", TranslationContext.MOVE),
        ("电", "Electric", TranslationContext.TYPE),
    ]
    
    print("\nTesting Chinese to English translation:")
    for chinese, expected_english, context in test_cases:
        result = translation_manager.translate_to_english(chinese, context)
        if result:
            print(f"✅ {chinese} -> {result} (context: {context.value})")
        else:
            print(f"❌ {chinese} -> No translation found")
    
    return True


async def test_special_cases():
    """Test special case translations (like Psychic)"""
    print("\n=== Testing Special Cases ===")
    
    # Test Psychic in different contexts
    psychic_tests = [
        (TranslationContext.MOVE, "精神强念"),
        (TranslationContext.TYPE, "超能力"),
        (TranslationContext.ABILITY, "超能力"),
        (None, "超能力")  # Default case
    ]
    
    print("\nTesting 'Psychic' in different contexts:")
    for context, expected in psychic_tests:
        result = translation_manager.translate_to_chinese("Psychic", context)
        context_name = context.value if context else "default"
        if result == expected:
            print(f"✅ Psychic -> {result} (context: {context_name})")
        else:
            print(f"❌ Psychic -> {result}, expected: {expected} (context: {context_name})")
    
    # Test Hidden Power Psychic
    result = translation_manager.translate_to_chinese("Hidden Power Psychic", TranslationContext.MOVE)
    if result:
        print(f"✅ Hidden Power Psychic -> {result}")
    else:
        print("❌ Hidden Power Psychic -> No translation found")
    
    return True


async def test_display_translator():
    """Test display translator functionality"""
    print("\n=== Testing Display Translator ===")
    
    # Test Pokemon info translation
    pokemon_data = {
        "name": "Pikachu",
        "types": ["Electric"],
        "abilities": {"0": "Static", "H": "Lightning Rod"}
    }
    
    translated = display_translator.translate_pokemon_info(pokemon_data)
    print(f"Original Pokemon data: {pokemon_data}")
    print(f"Translated Pokemon data: {translated}")
    
    # Test move info translation
    move_data = {
        "name": "Psychic",
        "type": "Psychic"
    }
    
    translated_move = display_translator.translate_move_info(move_data)
    print(f"\nOriginal Move data: {move_data}")
    print(f"Translated Move data: {translated_move}")
    
    # Test stat names
    stat_names = display_translator.format_stat_names()
    print(f"\nStat names: {stat_names}")
    
    # Test category names
    category_names = display_translator.format_category_names()
    print(f"Category names: {category_names}")
    
    return True


async def test_battle_log_translation():
    """Test battle log translation"""
    print("\n=== Testing Battle Log Translation ===")
    
    # Test battle log entries
    log_entries = [
        "Pikachu used Psychic!",
        "The Psychic-type move was super effective!",
        "Alakazam's Psychic hit the target!"
    ]
    
    print("Testing battle log translation:")
    for entry in log_entries:
        translated = display_translator.translate_battle_log(entry)
        print(f"Original: {entry}")
        print(f"Translated: {translated}")
        print()
    
    return True


async def test_context_awareness():
    """Test context-aware translations"""
    print("\n=== Testing Context Awareness ===")
    
    # Test type translations
    types = ["Fire", "Water", "Grass", "Electric", "Psychic", "Dark"]
    
    print("Testing type translations:")
    for type_name in types:
        chinese = translation_manager.translate_to_chinese(type_name, TranslationContext.TYPE)
        print(f"  {type_name} -> {chinese}")
    
    # Test reverse translation
    print("\nTesting reverse type translations:")
    chinese_types = ["火", "水", "草", "电", "超能力", "恶"]
    for chinese_type in chinese_types:
        english = translation_manager.translate_to_english(chinese_type, TranslationContext.TYPE)
        print(f"  {chinese_type} -> {english}")
    
    return True


async def main():
    """Run all tests"""
    print("Enhanced Translation System Test")
    print("=" * 50)
    
    tests = [
        test_basic_translation,
        test_special_cases,
        test_display_translator,
        test_battle_log_translation,
        test_context_awareness
    ]
    
    all_passed = True
    for test in tests:
        try:
            result = await test()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with error: {e}")
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✅ All tests completed!")
    else:
        print("❌ Some tests failed!")
    
    return all_passed


if __name__ == "__main__":
    asyncio.run(main())
