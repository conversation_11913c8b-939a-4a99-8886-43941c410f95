#!/usr/bin/env python3
"""Test script for Zygarde lookup"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_zygarde_lookup():
    """Test Zygarde lookup with different name formats"""
    try:
        from src.plugins.pokemon.database import PokemonDatabase
        
        db = PokemonDatabase()
        success = await db.initialize()
        
        if not success:
            print("Failed to initialize database")
            return False
        
        # Test different Zygarde name formats
        test_names = [
            "Zygarde",
            "Zygarde-10%",
            "Zygarde-50%", 
            "Zygarde-Complete",
            "zygarde10",
            "zygarde50",
            "zygardecomplete"
        ]
        
        print("Testing Zygarde lookup with different name formats:")
        print("=" * 50)
        
        for name in test_names:
            pokemon = await db.get_pokemon(name)
            if pokemon:
                print(f"✓ '{name}' -> Found: {pokemon.name}")
            else:
                print(f"✗ '{name}' -> Not found")
        
        # Also test the normalization methods directly
        print("\nTesting normalization methods:")
        print("=" * 30)
        
        for name in test_names:
            normalized = db._normalize_pokemon_id(name)
            alternatives = db._get_pokemon_id_alternatives(name)
            print(f"'{name}' -> normalized: '{normalized}', alternatives: {alternatives}")
        
        return True
        
    except Exception as e:
        print(f"Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(test_zygarde_lookup())
    sys.exit(0 if result else 1)
