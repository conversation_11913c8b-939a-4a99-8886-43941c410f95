"""
Admin Commands

Nonebot admin commands for Pokemon plugin management.
"""

import logging
from typing import Optional
from nonebot import on_command, get_driver
from nonebot.adapters.onebot.v11 import Bot, Event, MessageEvent
from nonebot.params import CommandArg
from nonebot.typing import T_State
from nonebot.permission import SUPERUSER
from nonebot.exception import FinishedException

from ..database import PokemonDatabase
from ..battle import BattleManager
from ..showdown_bridge import ShowdownBridge
from ..config import config

from nonebot import logger

# Global instances
pokemon_db: Optional[PokemonDatabase] = None
battle_manager: Optional[BattleManager] = None
showdown_bridge: Optional[ShowdownBridge] = None

# Admin command handlers (restricted to superusers)
admin_stats = on_command("pokemon admin stats", permission=SUPERUSER, priority=1)
admin_shutdown = on_command("pokemon admin shutdown", permission=SUPERUSER, priority=1)
admin_refresh = on_command("pokemon admin refresh", permission=SUPERUSER, priority=1)


@admin_stats.handle()
async def handle_admin_stats(bot: Bo<PERSON>, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle admin stats command"""
    try:
        stats_lines = ["🔧 Pokemon Plugin 系统状态:"]
        
        # Database stats
        if pokemon_db:
            db_stats = await pokemon_db.get_database_stats()
            stats_lines.extend([
                "",
                "📊 数据库状态:",
                f"  初始化: {'✅' if db_stats.get('initialized') else '❌'}",
                f"  宝可梦数量: {db_stats.get('pokemon_count', 0)}",
                f"  技能数量: {db_stats.get('moves_count', 0)}",
                f"  道具数量: {db_stats.get('items_count', 0)}",
                f"  特性数量: {db_stats.get('abilities_count', 0)}"
            ])
            
            cache_info = db_stats.get('cache_info', {})
            if cache_info:
                stats_lines.extend([
                    f"  缓存条目: {cache_info.get('memory_entries', 0)}",
                    f"  磁盘缓存: {cache_info.get('disk_entries', 0)} 个文件"
                ])
        else:
            stats_lines.append("\n📊 数据库状态: ❌ 未初始化")
        
        # Battle manager stats
        if battle_manager:
            battle_stats = await battle_manager.get_battle_stats()
            stats_lines.extend([
                "",
                "⚔️ 战斗系统状态:",
                f"  总对战数: {battle_stats.get('total_battles', 0)}",
                f"  进行中: {battle_stats.get('active_battles', 0)}",
                f"  已完成: {battle_stats.get('finished_battles', 0)}",
                f"  参与用户: {battle_stats.get('users_in_battles', 0)}"
            ])
        else:
            stats_lines.append("\n⚔️ 战斗系统状态: ❌ 未初始化")
        
        # Showdown bridge stats
        if showdown_bridge:
            health = await showdown_bridge.health_check()
            stats_lines.extend([
                "",
                "🌉 Showdown 桥接状态:",
                f"  初始化: {'✅' if health.get('initialized') else '❌'}",
                f"  进程数: {health.get('active_processes', 0)}",
                f"  活跃对战: {health.get('active_battles', 0)}",
                f"  Showdown 路径: {'✅' if health.get('showdown_path_exists') else '❌'}"
            ])
        else:
            stats_lines.append("\n🌉 Showdown 桥接状态: ❌ 未初始化")
        
        # Configuration
        stats_lines.extend([
            "",
            "⚙️ 配置信息:",
            f"  调试模式: {'✅' if config.debug_mode else '❌'}",
            f"  默认格式: {config.default_format}",
            f"  最大并发对战: {config.max_concurrent_battles}",
            f"  对战超时: {config.battle_timeout}s",
            f"  数据缓存: {'✅' if config.cache_pokemon_data else '❌'}"
        ])
        
        await admin_stats.finish("\n".join(stats_lines))
        
    except FinishedException:
        raise
    except Exception as e:
        logger.error(f"Error in admin stats: {e}")
        await admin_stats.finish("获取系统状态时出现错误")


@admin_shutdown.handle()
async def handle_admin_shutdown(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle admin shutdown command"""
    try:
        await admin_shutdown.send("正在关闭 Pokemon Plugin...")
        
        # Shutdown battle manager
        if battle_manager:
            await battle_manager.shutdown()
            await admin_shutdown.send("✅ 战斗系统已关闭")
        
        # Shutdown showdown bridge
        if showdown_bridge:
            await showdown_bridge.shutdown()
            await admin_shutdown.send("✅ Showdown 桥接已关闭")
        
        await admin_shutdown.finish("🔧 Pokemon Plugin 已完全关闭")
        
    except FinishedException:
        raise
    except Exception as e:
        logger.error(f"Error in admin shutdown: {e}")
        await admin_shutdown.finish("关闭系统时出现错误")


@admin_refresh.handle()
async def handle_admin_refresh(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle admin refresh command"""
    try:
        args_str = str(args).strip()
        
        if not args_str or args_str == "data":
            # Refresh Pokemon data
            if pokemon_db:
                await admin_refresh.send("正在刷新宝可梦数据...")
                success = await pokemon_db.refresh_data()
                if success:
                    await admin_refresh.send("✅ 宝可梦数据刷新完成")
                else:
                    await admin_refresh.send("❌ 宝可梦数据刷新失败")
            else:
                await admin_refresh.send("❌ 数据库未初始化")
        
        elif args_str == "cache":
            # Clear cache
            if pokemon_db:
                await admin_refresh.send("正在清理缓存...")
                await pokemon_db.cache_manager.clear()
                await admin_refresh.send("✅ 缓存清理完成")
            else:
                await admin_refresh.send("❌ 数据库未初始化")
        
        elif args_str == "showdown":
            # Restart showdown bridge
            if showdown_bridge:
                await admin_refresh.send("正在重启 Showdown 桥接...")
                await showdown_bridge.shutdown()
                success = await showdown_bridge.initialize()
                if success:
                    await admin_refresh.send("✅ Showdown 桥接重启完成")
                else:
                    await admin_refresh.send("❌ Showdown 桥接重启失败")
            else:
                await admin_refresh.send("❌ Showdown 桥接未初始化")
        
        else:
            await admin_refresh.finish(
                "使用方法: /pokemon admin refresh [data|cache|showdown]\n"
                "  data - 刷新宝可梦数据\n"
                "  cache - 清理缓存\n"
                "  showdown - 重启 Showdown 桥接\n"
                "  (无参数默认刷新数据)"
            )
        
    except FinishedException:
        raise
    except Exception as e:
        logger.error(f"Error in admin refresh: {e}")
        await admin_refresh.finish("刷新操作时出现错误")


# Initialize global instances when plugin loads
@get_driver().on_startup
async def init_admin_commands():
    """Initialize admin commands"""
    global pokemon_db, battle_manager, showdown_bridge
    
    # Import from main plugin
    from .. import pokemon_db as pdb, battle_manager as bm, showdown_bridge as sb
    
    pokemon_db = pdb
    battle_manager = bm
    showdown_bridge = sb
