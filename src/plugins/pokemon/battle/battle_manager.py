"""
Battle Manager

Main manager for Pokemon battles. Coordinates battle creation, management,
and cleanup.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any, Tuple
from datetime import datetime, timedelta
import uuid

from .battle_handler import <PERSON><PERSON>and<PERSON>
from .team_validator import TeamValida<PERSON>
from ..models import Battle, BattleFormat, BattleStatus, Team, BattlePlayer
from ..database import PokemonDatabase
from ..showdown_bridge import ShowdownBridge
from ..config import config

from nonebot import logger


class BattleManager:
    """Manages all Pokemon battles"""
    
    def __init__(self):
        self.database: Optional[PokemonDatabase] = None
        self.showdown_bridge: Optional[ShowdownBridge] = None
        self.team_validator: Optional[TeamValidator] = None
        
        self.active_battles: Dict[str, BattleHandler] = {}
        self.battle_history: List[Battle] = []
        self.user_battles: Dict[str, List[str]] = {}  # user_id -> battle_ids
        
        self._cleanup_task: Optional[asyncio.Task] = None
        self._initialized = False
        self._lock = asyncio.Lock()
    
    async def initialize(self, database: PokemonDatabase, showdown_bridge: ShowdownBridge) -> bool:
        """Initialize the battle manager"""
        try:
            self.database = database
            self.showdown_bridge = showdown_bridge
            self.team_validator = TeamValidator(database, showdown_bridge)
            
            # Start cleanup task
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            
            self._initialized = True
            logger.info("Battle manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing battle manager: {e}")
            return False
    
    async def create_battle(
        self,
        format: BattleFormat,
        player1_team: Team,
        player1_username: str,
        player1_user_id: str,
        player2_team: Team,
        player2_username: str,
        player2_user_id: str
    ) -> Optional[BattleHandler]:
        """Create a new battle"""
        if not self._initialized:
            logger.error("Battle manager not initialized")
            return None
        
        try:
            async with self._lock:
                # Check if users are already in battles
                if await self._user_in_active_battle(player1_user_id):
                    logger.warning(f"User {player1_user_id} is already in an active battle")
                    return None
                
                if await self._user_in_active_battle(player2_user_id):
                    logger.warning(f"User {player2_user_id} is already in an active battle")
                    return None
                
                # Validate teams
                team1_valid, team1_errors = await self.team_validator.validate_team(player1_team, format)
                if not team1_valid:
                    logger.warning(f"Player 1 team validation failed: {team1_errors}")
                    return None
                
                team2_valid, team2_errors = await self.team_validator.validate_team(player2_team, format)
                if not team2_valid:
                    logger.warning(f"Player 2 team validation failed: {team2_errors}")
                    return None
                
                # Create battle through Showdown bridge
                battle = await self.showdown_bridge.create_battle(
                    format, player1_team, player1_username, player2_team, player2_username
                )
                
                if not battle:
                    logger.error("Failed to create battle in Showdown")
                    return None
                
                # Update battle with user IDs
                battle.players["p1"].user_id = player1_user_id
                battle.players["p2"].user_id = player2_user_id
                
                # Create battle handler
                battle_handler = BattleHandler(battle, self.showdown_bridge)
                
                # Add to active battles
                self.active_battles[battle.id] = battle_handler
                
                # Track user battles
                if player1_user_id not in self.user_battles:
                    self.user_battles[player1_user_id] = []
                self.user_battles[player1_user_id].append(battle.id)
                
                if player2_user_id not in self.user_battles:
                    self.user_battles[player2_user_id] = []
                self.user_battles[player2_user_id].append(battle.id)
                
                # Add cleanup callback
                battle_handler.add_callback(self._battle_event_callback)
                
                # Start the battle
                await battle_handler.start_battle()
                
                logger.info(f"Created battle {battle.id} between {player1_username} and {player2_username}")
                return battle_handler
                
        except Exception as e:
            logger.error(f"Error creating battle: {e}")
            return None
    
    async def get_battle(self, battle_id: str) -> Optional[BattleHandler]:
        """Get a battle by ID"""
        return self.active_battles.get(battle_id)
    
    async def get_user_battles(self, user_id: str) -> List[BattleHandler]:
        """Get all active battles for a user"""
        battle_ids = self.user_battles.get(user_id, [])
        battles = []
        
        for battle_id in battle_ids:
            battle_handler = self.active_battles.get(battle_id)
            if battle_handler and battle_handler.is_active():
                battles.append(battle_handler)
        
        return battles
    
    async def _user_in_active_battle(self, user_id: str) -> bool:
        """Check if user is in an active battle"""
        user_battles = await self.get_user_battles(user_id)
        return len(user_battles) > 0

    async def submit_battle_action(self, battle_id: str, user_id: str, action: str) -> bool:
        """Submit a battle action"""
        try:
            battle_handler = self.active_battles.get(battle_id)
            if not battle_handler:
                logger.warning(f"Battle {battle_id} not found")
                return False

            # Find player ID for user
            player_id = None
            for pid, player in battle_handler.battle.players.items():
                if player.user_id == user_id:
                    player_id = pid
                    break

            if not player_id:
                logger.warning(f"User {user_id} not in battle {battle_id}")
                return False

            return await battle_handler.submit_action(player_id, action)

        except Exception as e:
            logger.error(f"Error submitting battle action: {e}")
            return False

    async def forfeit_battle(self, battle_id: str, user_id: str) -> bool:
        """Forfeit a battle"""
        try:
            battle_handler = self.active_battles.get(battle_id)
            if not battle_handler:
                return False

            # Find player ID for user
            player_id = None
            for pid, player in battle_handler.battle.players.items():
                if player.user_id == user_id:
                    player_id = pid
                    break

            if not player_id:
                return False

            return await battle_handler.forfeit(player_id)

        except Exception as e:
            logger.error(f"Error forfeiting battle: {e}")
            return False

    async def cancel_battle(self, battle_id: str, reason: str = "Cancelled") -> bool:
        """Cancel a battle"""
        try:
            battle_handler = self.active_battles.get(battle_id)
            if not battle_handler:
                return False

            return await battle_handler.cancel(reason)

        except Exception as e:
            logger.error(f"Error cancelling battle: {e}")
            return False

    async def list_active_battles(self) -> List[Dict[str, Any]]:
        """List all active battles"""
        battles = []
        for battle_handler in self.active_battles.values():
            if battle_handler.is_active():
                battles.append(battle_handler.get_battle_summary())
        return battles

    async def get_battle_stats(self) -> Dict[str, Any]:
        """Get battle statistics"""
        active_count = len([b for b in self.active_battles.values() if b.is_active()])
        finished_count = len([b for b in self.active_battles.values() if b.is_finished()])

        return {
            "total_battles": len(self.active_battles),
            "active_battles": active_count,
            "finished_battles": finished_count,
            "total_history": len(self.battle_history),
            "users_in_battles": len(self.user_battles)
        }

    async def _battle_event_callback(self, event_type: str, data: Dict[str, Any]):
        """Handle battle events"""
        try:
            if event_type in ["battle_finished", "battle_cancelled", "battle_error"]:
                battle = data.get("battle")
                if battle:
                    await self._cleanup_battle(battle.id)

        except Exception as e:
            logger.error(f"Error in battle event callback: {e}")

    async def _cleanup_battle(self, battle_id: str):
        """Clean up a finished battle"""
        try:
            async with self._lock:
                battle_handler = self.active_battles.get(battle_id)
                if not battle_handler:
                    return

                # Move to history
                self.battle_history.append(battle_handler.battle)

                # Remove from active battles
                del self.active_battles[battle_id]

                # Clean up user battle tracking
                for user_id, battle_ids in self.user_battles.items():
                    if battle_id in battle_ids:
                        battle_ids.remove(battle_id)
                        if not battle_ids:
                            del self.user_battles[user_id]

                # Clean up battle handler
                await battle_handler.cleanup()

                logger.debug(f"Cleaned up battle {battle_id}")

        except Exception as e:
            logger.error(f"Error cleaning up battle {battle_id}: {e}")

    async def _cleanup_loop(self):
        """Periodic cleanup of timed out battles"""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute

                timed_out_battles = []
                for battle_id, battle_handler in self.active_battles.items():
                    if battle_handler.is_timed_out():
                        timed_out_battles.append(battle_id)

                for battle_id in timed_out_battles:
                    logger.info(f"Battle {battle_id} timed out, cleaning up")
                    await self.cancel_battle(battle_id, "Timed out")

                # Clean up old battle history
                if len(self.battle_history) > 1000:
                    self.battle_history = self.battle_history[-500:]

            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")

    async def shutdown(self):
        """Shutdown the battle manager"""
        try:
            logger.info("Shutting down battle manager...")

            # Cancel cleanup task
            if self._cleanup_task:
                self._cleanup_task.cancel()
                try:
                    await self._cleanup_task
                except asyncio.CancelledError:
                    pass

            # Cancel all active battles
            for battle_id in list(self.active_battles.keys()):
                await self.cancel_battle(battle_id, "System shutdown")

            # Clean up all battles
            for battle_handler in list(self.active_battles.values()):
                await battle_handler.cleanup()

            self.active_battles.clear()
            self.user_battles.clear()

            logger.info("Battle manager shut down successfully")

        except Exception as e:
            logger.error(f"Error shutting down battle manager: {e}")
