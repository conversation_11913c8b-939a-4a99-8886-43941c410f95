"""
Pokemon Team Validator

Validates Pokemon teams for battle formats using Pokemon Showdown's validation logic.
"""

import logging
from typing import List, Dict, Any, Tuple, Optional
import asyncio

from ..models import Team, PokemonSet, BattleFormat
from ..database import PokemonDatabase
from ..showdown_bridge import ShowdownBridge
from ..config import config

from nonebot import logger


class TeamValidator:
    """Validates Pokemon teams for specific battle formats"""
    
    def __init__(self, database: PokemonDatabase, showdown_bridge: ShowdownBridge):
        self.database = database
        self.showdown_bridge = showdown_bridge
    
    async def validate_team(self, team: Team, format: BattleFormat) -> Tuple[bool, List[str]]:
        """Validate a team for a specific format"""
        try:
            errors = []
            
            # Basic validation
            basic_errors = await self._validate_basic_team_structure(team)
            errors.extend(basic_errors)
            
            # Format-specific validation
            format_errors = await self._validate_format_specific(team, format)
            errors.extend(format_errors)
            
            # Pokemon-specific validation
            for i, pokemon in enumerate(team.pokemon):
                pokemon_errors = await self._validate_pokemon(pokemon, format, i + 1)
                errors.extend(pokemon_errors)
            
            # Team composition validation
            composition_errors = await self._validate_team_composition(team, format)
            errors.extend(composition_errors)
            
            # Use Pokemon Showdown's validator if available
            if self.showdown_bridge.is_initialized:
                showdown_valid, showdown_errors = await self.showdown_bridge.validate_team(team, format)
                if not showdown_valid:
                    errors.extend(showdown_errors)
            
            is_valid = len(errors) == 0
            return is_valid, errors
            
        except Exception as e:
            logger.error(f"Error validating team: {e}")
            return False, [f"Validation error: {str(e)}"]
    
    async def _validate_basic_team_structure(self, team: Team) -> List[str]:
        """Validate basic team structure"""
        errors = []
        
        # Check team size
        if len(team.pokemon) == 0:
            errors.append("Team cannot be empty")
            return errors
        
        if len(team.pokemon) > 6:
            errors.append("Team cannot have more than 6 Pokemon")
        
        # Check for duplicate Pokemon
        species_count = {}
        for i, pokemon in enumerate(team.pokemon):
            species = pokemon.species.lower()
            if species in species_count:
                errors.append(f"Duplicate Pokemon: {pokemon.species} (positions {species_count[species]} and {i + 1})")
            else:
                species_count[species] = i + 1
        
        return errors
    
    async def _validate_format_specific(self, team: Team, format: BattleFormat) -> List[str]:
        """Validate format-specific rules"""
        errors = []
        
        # Get format info
        format_info = await self.showdown_bridge.get_format_info(format)
        
        if format_info:
            # Check team size requirements
            required_size = format_info.get("team_size", 6)
            if len(team.pokemon) != required_size:
                errors.append(f"Format {format.value} requires exactly {required_size} Pokemon")
            
            # Check level cap
            level_cap = format_info.get("level_cap", 100)
            for i, pokemon in enumerate(team.pokemon):
                if pokemon.level > level_cap:
                    errors.append(f"Pokemon {i + 1} ({pokemon.species}) level {pokemon.level} exceeds format limit of {level_cap}")
        
        # Format-specific rules
        if format == BattleFormat.GEN9LC:
            # Little Cup specific rules
            for i, pokemon in enumerate(team.pokemon):
                if pokemon.level != 5:
                    errors.append(f"Little Cup requires all Pokemon to be level 5 (Pokemon {i + 1} is level {pokemon.level})")
                
                # Check if Pokemon can be level 5 (not evolved)
                pokemon_data = await self.database.get_pokemon(pokemon.species)
                if pokemon_data and pokemon_data.tier and "NFE" not in pokemon_data.tier:
                    # This is a simplified check - in reality, we'd need to check evolution data
                    pass
        
        elif format == BattleFormat.GEN9MONOTYPE:
            # Monotype specific rules
            if len(team.pokemon) > 0:
                # All Pokemon must share at least one type
                first_pokemon = await self.database.get_pokemon(team.pokemon[0].species)
                if first_pokemon:
                    shared_types = set(t.value for t in first_pokemon.types)
                    
                    for i, pokemon in enumerate(team.pokemon[1:], 2):
                        pokemon_data = await self.database.get_pokemon(pokemon.species)
                        if pokemon_data:
                            pokemon_types = set(t.value for t in pokemon_data.types)
                            shared_types &= pokemon_types
                    
                    if not shared_types:
                        errors.append("Monotype format requires all Pokemon to share at least one type")
        
        return errors
    
    async def _validate_pokemon(self, pokemon: PokemonSet, format: BattleFormat, position: int) -> List[str]:
        """Validate an individual Pokemon"""
        errors = []
        
        # Check if Pokemon exists
        pokemon_data = await self.database.get_pokemon(pokemon.species)
        if not pokemon_data:
            errors.append(f"Pokemon {position}: Unknown species '{pokemon.species}'")
            return errors
        
        # Check if Pokemon is legal in format
        if pokemon_data.is_nonstandard:
            if pokemon_data.is_nonstandard not in ["Past", "Unobtainable"]:
                errors.append(f"Pokemon {position}: {pokemon.species} is not legal in this format ({pokemon_data.is_nonstandard})")
        
        # Validate moves
        move_errors = await self._validate_moves(pokemon, pokemon_data, position)
        errors.extend(move_errors)
        
        # Validate ability
        ability_errors = await self._validate_ability(pokemon, pokemon_data, position)
        errors.extend(ability_errors)
        
        # Validate item
        item_errors = await self._validate_item(pokemon, position)
        errors.extend(item_errors)
        
        # Validate stats
        stat_errors = await self._validate_stats(pokemon, position)
        errors.extend(stat_errors)
        
        return errors
    
    async def _validate_moves(self, pokemon: PokemonSet, pokemon_data, position: int) -> List[str]:
        """Validate Pokemon moves"""
        errors = []
        
        if len(pokemon.moves) == 0:
            errors.append(f"Pokemon {position} ({pokemon.species}): Must have at least one move")
            return errors
        
        if len(pokemon.moves) > 4:
            errors.append(f"Pokemon {position} ({pokemon.species}): Cannot have more than 4 moves")
        
        # Check for duplicate moves
        move_names = [move.lower() for move in pokemon.moves]
        if len(move_names) != len(set(move_names)):
            errors.append(f"Pokemon {position} ({pokemon.species}): Cannot have duplicate moves")
        
        # Validate each move
        for move_name in pokemon.moves:
            move_data = await self.database.get_move(move_name)
            if not move_data:
                errors.append(f"Pokemon {position} ({pokemon.species}): Unknown move '{move_name}'")
            elif move_data.is_nonstandard:
                if move_data.is_nonstandard not in ["Past"]:
                    errors.append(f"Pokemon {position} ({pokemon.species}): Move '{move_name}' is not legal ({move_data.is_nonstandard})")
        
        return errors
    
    async def _validate_ability(self, pokemon: PokemonSet, pokemon_data, position: int) -> List[str]:
        """Validate Pokemon ability"""
        errors = []
        
        if pokemon.ability:
            # Check if ability exists
            ability_data = await self.database.get_ability(pokemon.ability)
            if not ability_data:
                errors.append(f"Pokemon {position} ({pokemon.species}): Unknown ability '{pokemon.ability}'")
            elif ability_data.is_nonstandard:
                errors.append(f"Pokemon {position} ({pokemon.species}): Ability '{pokemon.ability}' is not legal ({ability_data.is_nonstandard})")
            
            # Check if Pokemon can have this ability
            valid_abilities = list(pokemon_data.abilities.values())
            if pokemon.ability not in valid_abilities:
                errors.append(f"Pokemon {position} ({pokemon.species}): Cannot have ability '{pokemon.ability}'. Valid abilities: {', '.join(valid_abilities)}")
        
        return errors
    
    async def _validate_item(self, pokemon: PokemonSet, position: int) -> List[str]:
        """Validate Pokemon item"""
        errors = []
        
        if pokemon.item:
            item_data = await self.database.get_item(pokemon.item)
            if not item_data:
                errors.append(f"Pokemon {position} ({pokemon.species}): Unknown item '{pokemon.item}'")
            elif item_data.is_nonstandard:
                errors.append(f"Pokemon {position} ({pokemon.species}): Item '{pokemon.item}' is not legal ({item_data.is_nonstandard})")
        
        return errors
    
    async def _validate_stats(self, pokemon: PokemonSet, position: int) -> List[str]:
        """Validate Pokemon stats (EVs, IVs, level)"""
        errors = []
        
        # Validate level
        if pokemon.level < 1 or pokemon.level > 100:
            errors.append(f"Pokemon {position} ({pokemon.species}): Level must be between 1 and 100")
        
        # Validate EVs
        total_evs = sum(pokemon.evs.values())
        if total_evs > 510:
            errors.append(f"Pokemon {position} ({pokemon.species}): Total EVs ({total_evs}) cannot exceed 510")
        
        for stat, ev in pokemon.evs.items():
            if ev < 0 or ev > 252:
                errors.append(f"Pokemon {position} ({pokemon.species}): {stat.upper()} EVs must be between 0 and 252")
        
        # Validate IVs
        for stat, iv in pokemon.ivs.items():
            if iv < 0 or iv > 31:
                errors.append(f"Pokemon {position} ({pokemon.species}): {stat.upper()} IVs must be between 0 and 31")
        
        return errors
    
    async def _validate_team_composition(self, team: Team, format: BattleFormat) -> List[str]:
        """Validate overall team composition"""
        errors = []
        
        # Check for item clause (no duplicate items except certain items)
        item_counts = {}
        allowed_duplicates = {"leftovers", "choiceband", "choicescarf", "choicespecs"}  # Simplified list
        
        for i, pokemon in enumerate(team.pokemon):
            if pokemon.item:
                item_lower = pokemon.item.lower()
                if item_lower not in allowed_duplicates:
                    if item_lower in item_counts:
                        errors.append(f"Item clause violation: {pokemon.item} used by multiple Pokemon")
                    else:
                        item_counts[item_lower] = i + 1
        
        return errors
    
    async def validate_pokemon_set(self, pokemon: PokemonSet, format: BattleFormat) -> Tuple[bool, List[str]]:
        """Validate a single Pokemon set"""
        try:
            errors = await self._validate_pokemon(pokemon, format, 1)
            return len(errors) == 0, errors
            
        except Exception as e:
            logger.error(f"Error validating Pokemon set: {e}")
            return False, [f"Validation error: {str(e)}"]
    
    async def suggest_fixes(self, team: Team, format: BattleFormat) -> List[str]:
        """Suggest fixes for team validation errors"""
        suggestions = []
        
        try:
            is_valid, errors = await self.validate_team(team, format)
            
            if is_valid:
                return ["Team is already valid!"]
            
            # Analyze errors and provide suggestions
            for error in errors:
                if "exceeds format limit" in error:
                    suggestions.append("Reduce Pokemon levels to match format requirements")
                elif "Unknown species" in error:
                    suggestions.append("Check Pokemon names for typos")
                elif "Unknown move" in error:
                    suggestions.append("Check move names for typos")
                elif "duplicate moves" in error:
                    suggestions.append("Remove duplicate moves from Pokemon")
                elif "Total EVs" in error and "exceed 510" in error:
                    suggestions.append("Redistribute EVs to not exceed 510 total")
                elif "Item clause violation" in error:
                    suggestions.append("Use different items on Pokemon (item clause)")
            
            if not suggestions:
                suggestions.append("Please check the validation errors and fix them manually")
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error generating suggestions: {e}")
            return ["Unable to generate suggestions due to an error"]
