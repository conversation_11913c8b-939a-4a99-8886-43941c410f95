"""
User Session Manager

Manages user sessions for Pokemon battles to simplify command usage.
"""

import asyncio
import logging
from typing import Dict, Optional, Set, Any
from datetime import datetime, timedelta

from nonebot import logger


class UserSession:
    """Represents a user's battle session"""
    
    def __init__(self, user_id: str):
        self.user_id = user_id
        self.current_battle_id: Optional[str] = None
        self.last_activity = datetime.now()
        self.battle_history: list[str] = []
        self.preferences: Dict[str, Any] = {}
        
    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = datetime.now()
        
    def set_current_battle(self, battle_id: str):
        """Set current active battle"""
        self.current_battle_id = battle_id
        if battle_id not in self.battle_history:
            self.battle_history.append(battle_id)
        self.update_activity()
        
    def clear_current_battle(self):
        """Clear current battle"""
        self.current_battle_id = None
        self.update_activity()
        
    def is_expired(self, timeout_minutes: int = 30) -> bool:
        """Check if session is expired"""
        return datetime.now() - self.last_activity > timedelta(minutes=timeout_minutes)


class UserSessionManager:
    """Manages user sessions for simplified battle commands"""
    
    def __init__(self):
        self.sessions: Dict[str, UserSession] = {}
        self._cleanup_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
        
    async def initialize(self):
        """Initialize the session manager"""
        if not self._cleanup_task:
            self._cleanup_task = asyncio.create_task(self._cleanup_expired_sessions())
            logger.info("User session manager initialized")
            
    async def shutdown(self):
        """Shutdown the session manager"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            self._cleanup_task = None
            
        self.sessions.clear()
        logger.info("User session manager shutdown")
        
    async def get_session(self, user_id: str) -> UserSession:
        """Get or create user session"""
        async with self._lock:
            if user_id not in self.sessions:
                self.sessions[user_id] = UserSession(user_id)
            
            session = self.sessions[user_id]
            session.update_activity()
            return session
            
    async def get_current_battle_id(self, user_id: str) -> Optional[str]:
        """Get user's current battle ID"""
        session = await self.get_session(user_id)
        return session.current_battle_id
        
    async def set_current_battle(self, user_id: str, battle_id: str):
        """Set user's current battle"""
        session = await self.get_session(user_id)
        session.set_current_battle(battle_id)
        
    async def clear_current_battle(self, user_id: str):
        """Clear user's current battle"""
        session = await self.get_session(user_id)
        session.clear_current_battle()
        
    async def get_battle_history(self, user_id: str) -> list[str]:
        """Get user's battle history"""
        session = await self.get_session(user_id)
        return session.battle_history.copy()
        
    async def set_preference(self, user_id: str, key: str, value: Any):
        """Set user preference"""
        session = await self.get_session(user_id)
        session.preferences[key] = value
        
    async def get_preference(self, user_id: str, key: str, default: Any = None) -> Any:
        """Get user preference"""
        session = await self.get_session(user_id)
        return session.preferences.get(key, default)
        
    async def get_active_users(self) -> Set[str]:
        """Get set of active user IDs"""
        async with self._lock:
            return {user_id for user_id, session in self.sessions.items() 
                   if not session.is_expired()}
                   
    async def _cleanup_expired_sessions(self):
        """Cleanup expired sessions periodically"""
        while True:
            try:
                await asyncio.sleep(300)  # Check every 5 minutes
                
                async with self._lock:
                    expired_users = [
                        user_id for user_id, session in self.sessions.items()
                        if session.is_expired()
                    ]
                    
                    for user_id in expired_users:
                        del self.sessions[user_id]
                        
                    if expired_users:
                        logger.debug(f"Cleaned up {len(expired_users)} expired user sessions")
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in session cleanup: {e}")


# Global session manager instance
session_manager = UserSessionManager()
