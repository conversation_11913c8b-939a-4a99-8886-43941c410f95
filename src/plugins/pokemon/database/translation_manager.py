"""
Translation Manager

Handles translation between Chinese and English names for Pokemon, moves, items, abilities, etc.
Supports context-aware translations and special cases.
"""

import json
import logging
import re
from typing import Dict, Optional, Set, List, Tuple
from pathlib import Path
from enum import Enum

from nonebot import logger


class TranslationContext(Enum):
    """Translation context types"""
    POKEMON = "pokemon"
    MOVE = "move"
    ABILITY = "ability"
    ITEM = "item"
    TYPE = "type"
    GENERAL = "general"


class TranslationManager:
    """Manages translations between Chinese and English names with context awareness"""

    def __init__(self):
        self._translations: Dict[str, str] = {}  # Chinese -> English
        self._reverse_translations: Dict[str, str] = {}  # English -> Chinese
        self._context_translations: Dict[TranslationContext, Dict[str, str]] = {}  # Context-specific translations
        self._special_cases: Dict[str, Dict[str, str]] = {}  # Special case translations
        self._initialized = False
        
    async def initialize(self) -> bool:
        """Initialize translation data"""
        if self._initialized:
            return True

        try:
            # Load translations from the JavaScript file
            translations_file = Path("data/pokemon_cache/translations.js")
            if not translations_file.exists():
                logger.warning("Translations file not found")
                return False

            await self._load_translations(translations_file)
            self._setup_special_cases()
            self._setup_context_translations()
            self._initialized = True
            logger.info(f"Loaded {len(self._translations)} translations with special cases")
            return True

        except Exception as e:
            logger.error(f"Error initializing translation manager: {e}")
            return False
    
    async def _load_translations(self, file_path: Path):
        """Load translations from JavaScript file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract the translations object from JavaScript
            # Look for patterns like "English Name": "中文名称"
            pattern = r'"([^"]+)":\s*"([^"]+)"'
            matches = re.findall(pattern, content)
            
            for english, chinese in matches:
                # Skip UI translations and focus on Pokemon-related content
                if self._is_pokemon_related(english, chinese):
                    # Normalize names for better matching
                    english_normalized = self._normalize_name(english)
                    chinese_normalized = self._normalize_name(chinese)
                    
                    # Store both directions
                    self._translations[chinese_normalized] = english_normalized
                    self._reverse_translations[english_normalized] = chinese_normalized
                    
                    # Also store original forms
                    self._translations[chinese] = english
                    self._reverse_translations[english] = chinese
                    
        except Exception as e:
            logger.error(f"Error loading translations from {file_path}: {e}")

    def _setup_special_cases(self):
        """Setup special case translations that depend on context"""
        # Psychic has different translations in different contexts
        self._special_cases["Psychic"] = {
            "move": "精神强念",  # When it's a move name
            "type": "超能力",    # When it's a type name
            "ability": "超能力", # When it's an ability context
            "general": "超能力"  # Default fallback
        }

        # Add more special cases as needed
        self._special_cases["Hidden Power Psychic"] = {
            "move": "觉醒力量-超能力",
            "general": "觉醒力量-超能力"
        }

    def _setup_context_translations(self):
        """Setup context-specific translation dictionaries"""
        for context in TranslationContext:
            self._context_translations[context] = {}

        # Populate context-specific translations
        # Types
        type_translations = {
            "Normal": "一般", "Fire": "火", "Water": "水", "Electric": "电",
            "Grass": "草", "Ice": "冰", "Fighting": "格斗", "Poison": "毒",
            "Ground": "地面", "Flying": "飞行", "Psychic": "超能力", "Bug": "虫",
            "Rock": "岩石", "Ghost": "幽灵", "Dragon": "龙", "Dark": "恶",
            "Steel": "钢", "Fairy": "妖精"
        }

        for english, chinese in type_translations.items():
            self._context_translations[TranslationContext.TYPE][english] = chinese
            self._context_translations[TranslationContext.TYPE][chinese] = english

    def _is_pokemon_related(self, english: str, chinese: str) -> bool:
        """Check if a translation pair is Pokemon-related""" 
        # Include if it contains Pokemon-related patterns
        pokemon_patterns = [
            # Pokemon names (usually capitalized single words or hyphenated)
            r'^[A-Z][a-z]+(-[A-Z][a-z]+)*$',
            # Move names (can be multiple words)
            r'^[A-Z][a-z]+(\s+[A-Z][a-z]+)*$',
            # Item names (often end with specific suffixes)
            r'.*(Berry|Stone|Orb|Plate|Drive|Memory|Z)$',
            # Ability names
            r'^[A-Z][a-z]+(\s+[A-Z][a-z]+)*$'
        ]
        
        # Check if English name matches Pokemon-related patterns
        for pattern in pokemon_patterns:
            if re.match(pattern, english):
                return True
                
        # Include if Chinese contains Pokemon-related characters
        pokemon_chars = ['宝可梦', '精灵', '球', '果', '石', '珠', '板', '卡', '带']
        if any(char in chinese for char in pokemon_chars):
            return True
            
        return False
    
    def _normalize_name(self, name: str) -> str:
        """Normalize name for better matching"""
        # Remove special characters and convert to lowercase
        normalized = re.sub(r'[^\w\u4e00-\u9fff]', '', name.lower())
        return normalized
    
    def translate_to_english(self, chinese_name: str, context: Optional[TranslationContext] = None) -> Optional[str]:
        """Translate Chinese name to English with context awareness"""
        if not self._initialized:
            return None

        # Check context-specific translations first
        if context and context in self._context_translations:
            if chinese_name in self._context_translations[context]:
                return self._context_translations[context][chinese_name]

        # Try exact match
        if chinese_name in self._translations:
            return self._translations[chinese_name]

        # Try normalized match
        normalized = self._normalize_name(chinese_name)
        if normalized in self._translations:
            return self._translations[normalized]

        # Try partial matching for Pokemon names
        for chinese_key, english_value in self._translations.items():
            if chinese_name in chinese_key or chinese_key in chinese_name:
                return english_value

        return None

    def translate_to_chinese(self, english_name: str, context: Optional[TranslationContext] = None) -> Optional[str]:
        """Translate English name to Chinese with context awareness"""
        if not self._initialized:
            return None

        # Handle special cases first
        if english_name in self._special_cases:
            context_key = context.value if context else "general"
            if context_key in self._special_cases[english_name]:
                return self._special_cases[english_name][context_key]
            else:
                return self._special_cases[english_name]["general"]

        # Check context-specific translations
        if context and context in self._context_translations:
            if english_name in self._context_translations[context]:
                return self._context_translations[context][english_name]

        # Try exact match
        if english_name in self._reverse_translations:
            return self._reverse_translations[english_name]

        # Try normalized match
        normalized = self._normalize_name(english_name)
        if normalized in self._reverse_translations:
            return self._reverse_translations[normalized]

        return None
    
    def get_all_chinese_names(self) -> Set[str]:
        """Get all available Chinese names"""
        return set(self._translations.keys())
    
    def get_all_english_names(self) -> Set[str]:
        """Get all available English names"""
        return set(self._reverse_translations.keys())
    
    def search_chinese_names(self, query: str, limit: int = 10) -> List[str]:
        """Search for Chinese names containing the query"""
        query_lower = query.lower()
        results = []
        
        for chinese_name in self._translations.keys():
            if query_lower in chinese_name.lower() and len(results) < limit:
                results.append(chinese_name)
                
        return results
    
    def search_english_names(self, query: str, limit: int = 10) -> List[str]:
        """Search for English names containing the query"""
        query_lower = query.lower()
        results = []
        
        for english_name in self._reverse_translations.keys():
            if query_lower in english_name.lower() and len(results) < limit:
                results.append(english_name)
                
        return results


class DisplayTranslator:
    """Handles translation for display purposes with formatting support"""

    def __init__(self, translation_manager: TranslationManager):
        self.tm = translation_manager

    def translate_pokemon_info(self, pokemon_data: dict) -> dict:
        """Translate Pokemon information for display"""
        translated = pokemon_data.copy()

        # Translate Pokemon name
        if 'name' in translated:
            chinese_name = self.tm.translate_to_chinese(translated['name'], TranslationContext.POKEMON)
            if chinese_name:
                translated['chinese_name'] = chinese_name

        # Translate types
        if 'types' in translated:
            translated_types = []
            for type_name in translated['types']:
                chinese_type = self.tm.translate_to_chinese(type_name, TranslationContext.TYPE)
                translated_types.append(chinese_type or type_name)
            translated['chinese_types'] = translated_types

        # Translate abilities
        if 'abilities' in translated:
            translated_abilities = {}
            for slot, ability in translated['abilities'].items():
                chinese_ability = self.tm.translate_to_chinese(ability, TranslationContext.ABILITY)
                translated_abilities[slot] = chinese_ability or ability
            translated['chinese_abilities'] = translated_abilities

        return translated

    def translate_move_info(self, move_data: dict) -> dict:
        """Translate move information for display"""
        translated = move_data.copy()

        # Translate move name
        if 'name' in translated:
            chinese_name = self.tm.translate_to_chinese(translated['name'], TranslationContext.MOVE)
            if chinese_name:
                translated['chinese_name'] = chinese_name

        # Translate type
        if 'type' in translated:
            chinese_type = self.tm.translate_to_chinese(translated['type'], TranslationContext.TYPE)
            if chinese_type:
                translated['chinese_type'] = chinese_type

        return translated

    def translate_battle_log(self, log_entry: str) -> str:
        """Translate battle log entries"""
        # Handle common battle log patterns
        translated = log_entry

        # Replace Pokemon names
        for english_name, chinese_name in self.tm._reverse_translations.items():
            if english_name in translated:
                translated = translated.replace(english_name, chinese_name)

        # Handle special move name cases
        if "used Psychic" in translated:
            translated = translated.replace("used Psychic", "使用了精神强念")
        elif "Psychic-type" in translated:
            translated = translated.replace("Psychic-type", "超能力属性")

        return translated

    def format_stat_names(self) -> dict:
        """Get Chinese stat names"""
        return {
            "hp": "HP",
            "attack": "攻击",
            "defense": "防御",
            "special_attack": "特攻",
            "special_defense": "特防",
            "speed": "速度"
        }

    def format_category_names(self) -> dict:
        """Get Chinese category names"""
        return {
            "Physical": "物理",
            "Special": "特殊",
            "Status": "变化"
        }


# Global instances
translation_manager = TranslationManager()
display_translator = DisplayTranslator(translation_manager)
