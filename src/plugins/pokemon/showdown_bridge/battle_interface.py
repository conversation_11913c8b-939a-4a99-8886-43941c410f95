"""
Pokemon Showdown Battle Interface

Provides high-level interface for creating and managing battles
with Pokemon Showdown simulator.
"""

import asyncio
import json
import logging
import uuid
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

from .process_manager import ShowdownProcessManager, ShowdownProcess
from .message_parser import ShowdownMessageParser, ParsedMessage, MessageType
from ..models import Battle, BattleFormat, BattleStatus, Team, BattlePlayer, BattleRequest, BattleAction
from ..config import config

from nonebot import logger


class ShowdownBattleInterface:
    """High-level interface for Pokemon Showdown battles"""
    
    def __init__(self, process_manager: ShowdownProcessManager):
        self.process_manager = process_manager
        self.message_parser = ShowdownMessageParser()
        self.active_battles: Dict[str, Battle] = {}
        self.battle_callbacks: Dict[str, List[Callable]] = {}
        self._setup_message_handlers()
    
    def _setup_message_handlers(self):
        """Setup message handlers for all processes"""
        # This will be called when new processes are created
        pass
    
    async def create_battle(
        self, 
        format: BattleFormat, 
        player1: BattlePlayer, 
        player2: BattlePlayer
    ) -> Optional[Battle]:
        """Create a new battle"""
        try:
            # Assign battle to a process
            battle_id = str(uuid.uuid4())
            process_id = await self.process_manager.assign_battle_to_process(battle_id)
            
            if not process_id:
                logger.error("No available process for new battle")
                return None
            
            process = self.process_manager.get_process_for_battle(battle_id)
            if not process:
                logger.error(f"Failed to get process for battle {battle_id}")
                return None
            
            # Create battle object
            battle = Battle(
                id=battle_id,
                format=format,
                players={
                    "p1": player1,
                    "p2": player2
                },
                status=BattleStatus.WAITING,
                showdown_process_id=process_id
            )
            
            # Add message handler for this battle
            handler_id = f"battle_{battle_id}"
            process.add_message_handler(
                handler_id, 
                lambda msg: self._handle_battle_message(battle_id, msg)
            )
            
            # Start the battle in Pokemon Showdown
            success = await self._start_showdown_battle(process, battle)
            if not success:
                logger.error(f"Failed to start battle {battle_id} in Showdown")
                process.remove_message_handler(handler_id)
                await self.process_manager.remove_battle(battle_id)
                return None
            
            self.active_battles[battle_id] = battle
            logger.info(f"Created battle {battle_id} with format {format}")
            return battle
            
        except Exception as e:
            logger.error(f"Error creating battle: {e}")
            return None
    
    async def _start_showdown_battle(self, process: ShowdownProcess, battle: Battle) -> bool:
        """Start a battle in Pokemon Showdown"""
        try:
            # Send start command
            start_command = f'>start {{"formatid":"{battle.format.value}"}}'
            if not await process.send_command(start_command):
                return False
            
            # Send player 1
            p1_team = self._pack_team(battle.players["p1"].team)
            p1_command = f'>player p1 {{"name":"{battle.players["p1"].username}","team":"{p1_team}"}}'
            if not await process.send_command(p1_command):
                return False
            
            # Send player 2
            p2_team = self._pack_team(battle.players["p2"].team)
            p2_command = f'>player p2 {{"name":"{battle.players["p2"].username}","team":"{p2_team}"}}'
            if not await process.send_command(p2_command):
                return False
            
            battle.status = BattleStatus.TEAM_PREVIEW
            battle.started_at = datetime.now()
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting Showdown battle: {e}")
            return False
    
    def _pack_team(self, team: Team) -> str:
        """Pack a team into Pokemon Showdown format"""
        packed_pokemon = []
        
        for pokemon in team.pokemon:
            # Build Pokemon string in Showdown format
            parts = [pokemon.species]
            
            if pokemon.item:
                parts.append(f"@{pokemon.item}")
            
            if pokemon.ability:
                parts.append(f"Ability: {pokemon.ability}")
            
            if pokemon.level != 50:
                parts.append(f"Level: {pokemon.level}")
            
            if pokemon.shiny:
                parts.append("Shiny: Yes")
            
            # EVs
            ev_parts = []
            for stat, value in pokemon.evs.items():
                if value > 0:
                    stat_name = {
                        "hp": "HP", "atk": "Atk", "def": "Def",
                        "spa": "SpA", "spd": "SpD", "spe": "Spe"
                    }.get(stat, stat)
                    ev_parts.append(f"{value} {stat_name}")
            
            if ev_parts:
                parts.append(f"EVs: {' / '.join(ev_parts)}")
            
            if pokemon.nature:
                parts.append(f"{pokemon.nature} Nature")
            
            # IVs (only if not 31)
            iv_parts = []
            for stat, value in pokemon.ivs.items():
                if value != 31:
                    stat_name = {
                        "hp": "HP", "atk": "Atk", "def": "Def",
                        "spa": "SpA", "spd": "SpD", "spe": "Spe"
                    }.get(stat, stat)
                    iv_parts.append(f"{value} {stat_name}")
            
            if iv_parts:
                parts.append(f"IVs: {' / '.join(iv_parts)}")
            
            # Moves
            for move in pokemon.moves:
                parts.append(f"- {move}")
            
            packed_pokemon.append('\n'.join(parts))
        
        return '\n\n'.join(packed_pokemon)
    
    async def send_battle_action(self, battle_id: str, player_id: str, action: str) -> bool:
        """Send a battle action to Pokemon Showdown"""
        try:
            process = self.process_manager.get_process_for_battle(battle_id)
            if not process:
                logger.error(f"No process found for battle {battle_id}")
                return False
            
            # Convert player_id to Showdown format
            showdown_player = "p1" if player_id == "p1" else "p2"
            command = f">{showdown_player} {action}"
            
            success = await process.send_command(command)
            if success:
                logger.debug(f"Sent action for battle {battle_id}: {command}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending battle action: {e}")
            return False
    
    async def _handle_battle_message(self, battle_id: str, raw_message: str):
        """Handle a message from Pokemon Showdown for a specific battle"""
        try:
            parsed = self.message_parser.parse_message(raw_message)
            battle = self.active_battles.get(battle_id)
            
            if not battle:
                logger.warning(f"Received message for unknown battle {battle_id}")
                return
            
            # Update battle log
            battle.log.append(raw_message)
            
            # Handle different message types
            if parsed.message_type == MessageType.UPDATE:
                await self._handle_battle_update(battle, parsed)
            elif parsed.message_type == MessageType.SIDEUPDATE:
                await self._handle_side_update(battle, parsed)
            elif parsed.message_type == MessageType.END:
                await self._handle_battle_end(battle, parsed)
            elif parsed.message_type == MessageType.ERROR:
                await self._handle_battle_error(battle, parsed)
            
            # Notify callbacks
            await self._notify_battle_callbacks(battle_id, parsed)
            
        except Exception as e:
            logger.error(f"Error handling battle message: {e}")
    
    async def _handle_battle_update(self, battle: Battle, parsed: ParsedMessage):
        """Handle battle update message"""
        events = parsed.data.get("events", [])
        
        for event in events:
            event_type = event.get("type")
            
            if event_type == "start":
                battle.status = BattleStatus.ACTIVE
            elif event_type == "turn":
                battle.current_turn = event.get("turn_number", battle.current_turn)
            elif event_type == "win":
                battle.status = BattleStatus.FINISHED
                battle.winner = event.get("winner")
                battle.finished_at = datetime.now()
    
    async def _handle_side_update(self, battle: Battle, parsed: ParsedMessage):
        """Handle side update message (player-specific)"""
        player_id = parsed.player_id
        request_data = parsed.data.get("request")
        
        if request_data and player_id:
            # This is a request for player action
            # Store it for the battle system to handle
            pass
    
    async def _handle_battle_end(self, battle: Battle, parsed: ParsedMessage):
        """Handle battle end message"""
        battle.status = BattleStatus.FINISHED
        battle.finished_at = datetime.now()
        
        # Clean up
        await self._cleanup_battle(battle.id)
    
    async def _handle_battle_error(self, battle: Battle, parsed: ParsedMessage):
        """Handle battle error message"""
        error_msg = parsed.data.get("error", "Unknown error")
        logger.error(f"Battle {battle.id} error: {error_msg}")
        
        battle.status = BattleStatus.ERROR
        await self._cleanup_battle(battle.id)
    
    async def _cleanup_battle(self, battle_id: str):
        """Clean up a finished battle"""
        try:
            # Remove from active battles
            battle = self.active_battles.pop(battle_id, None)
            if not battle:
                return
            
            # Remove message handler
            process = self.process_manager.get_process_for_battle(battle_id)
            if process:
                handler_id = f"battle_{battle_id}"
                process.remove_message_handler(handler_id)
            
            # Remove from process manager
            await self.process_manager.remove_battle(battle_id)
            
            logger.info(f"Cleaned up battle {battle_id}")
            
        except Exception as e:
            logger.error(f"Error cleaning up battle {battle_id}: {e}")
    
    def add_battle_callback(self, battle_id: str, callback: Callable):
        """Add a callback for battle events"""
        if battle_id not in self.battle_callbacks:
            self.battle_callbacks[battle_id] = []
        self.battle_callbacks[battle_id].append(callback)
    
    def remove_battle_callback(self, battle_id: str, callback: Callable):
        """Remove a battle callback"""
        if battle_id in self.battle_callbacks:
            try:
                self.battle_callbacks[battle_id].remove(callback)
            except ValueError:
                pass
    
    async def _notify_battle_callbacks(self, battle_id: str, parsed_message: ParsedMessage):
        """Notify all callbacks for a battle"""
        callbacks = self.battle_callbacks.get(battle_id, [])
        for callback in callbacks:
            try:
                await callback(parsed_message)
            except Exception as e:
                logger.error(f"Error in battle callback: {e}")
    
    def get_battle(self, battle_id: str) -> Optional[Battle]:
        """Get a battle by ID"""
        return self.active_battles.get(battle_id)
    
    def list_active_battles(self) -> List[Battle]:
        """List all active battles"""
        return list(self.active_battles.values())
