"""
Pokemon Showdown Message Parser

Parses messages from Pokemon Showdown simulator and converts them into
structured data that can be used by the battle system.
"""

import json
import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from nonebot import logger


class MessageType(str, Enum):
    """Types of messages from Pokemon Showdown"""
    UPDATE = "update"
    SIDEUPDATE = "sideupdate"
    END = "end"
    ERROR = "error"
    REQUEST = "request"
    UNKNOWN = "unknown"


@dataclass
class ParsedMessage:
    """Parsed message from Pokemon Showdown"""
    message_type: MessageType
    battle_id: Optional[str] = None
    player_id: Optional[str] = None
    data: Dict[str, Any] = None
    raw_content: str = ""
    
    def __post_init__(self):
        if self.data is None:
            self.data = {}


class ShowdownMessageParser:
    """Parser for Pokemon Showdown simulator messages"""
    
    def __init__(self):
        self.battle_logs: Dict[str, List[str]] = {}
        
    def parse_message(self, raw_message: str) -> ParsedMessage:
        """Parse a raw message from Pokemon Showdown"""
        try:
            # Split message into type and data
            if '\n' in raw_message:
                message_type, data = raw_message.split('\n', 1)
            else:
                message_type = raw_message
                data = ""
            
            # Determine message type
            msg_type = self._determine_message_type(message_type)
            
            # Parse based on message type
            if msg_type == MessageType.UPDATE:
                return self._parse_update_message(data, raw_message)
            elif msg_type == MessageType.SIDEUPDATE:
                return self._parse_sideupdate_message(data, raw_message)
            elif msg_type == MessageType.END:
                return self._parse_end_message(data, raw_message)
            elif msg_type == MessageType.ERROR:
                return self._parse_error_message(data, raw_message)
            else:
                return ParsedMessage(
                    message_type=MessageType.UNKNOWN,
                    raw_content=raw_message
                )
                
        except Exception as e:
            logger.error(f"Error parsing message: {e}")
            return ParsedMessage(
                message_type=MessageType.ERROR,
                data={"error": str(e)},
                raw_content=raw_message
            )
    
    def _determine_message_type(self, message_type: str) -> MessageType:
        """Determine the type of message"""
        if message_type == "update":
            return MessageType.UPDATE
        elif message_type == "sideupdate":
            return MessageType.SIDEUPDATE
        elif message_type == "end":
            return MessageType.END
        elif "error" in message_type.lower():
            return MessageType.ERROR
        else:
            return MessageType.UNKNOWN
    
    def _parse_update_message(self, data: str, raw_message: str) -> ParsedMessage:
        """Parse an update message (battle log)"""
        lines = data.split('\n')
        battle_events = []
        battle_id = None
        
        for line in lines:
            if not line.strip():
                continue
                
            # Extract battle ID if present
            if line.startswith('|init|battle'):
                battle_id = self._extract_battle_id(line)
            
            # Parse battle events
            event = self._parse_battle_event(line)
            if event:
                battle_events.append(event)
        
        return ParsedMessage(
            message_type=MessageType.UPDATE,
            battle_id=battle_id,
            data={
                "events": battle_events,
                "raw_lines": lines
            },
            raw_content=raw_message
        )
    
    def _parse_sideupdate_message(self, data: str, raw_message: str) -> ParsedMessage:
        """Parse a sideupdate message (player-specific data)"""
        lines = data.split('\n')
        if not lines:
            return ParsedMessage(
                message_type=MessageType.SIDEUPDATE,
                raw_content=raw_message
            )
        
        # First line should contain player ID
        player_line = lines[0]
        player_id = None
        
        if player_line.startswith('p1') or player_line.startswith('p2'):
            player_id = player_line[:2]
        
        # Parse request data if present
        request_data = None
        for line in lines[1:]:
            if line.startswith('|request|'):
                try:
                    request_json = line[9:]  # Remove "|request|"
                    if request_json:
                        request_data = json.loads(request_json)
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse request JSON: {e}")
        
        return ParsedMessage(
            message_type=MessageType.SIDEUPDATE,
            player_id=player_id,
            data={
                "request": request_data,
                "raw_lines": lines
            },
            raw_content=raw_message
        )
    
    def _parse_end_message(self, data: str, raw_message: str) -> ParsedMessage:
        """Parse an end message (battle finished)"""
        return ParsedMessage(
            message_type=MessageType.END,
            data={"end_data": data},
            raw_content=raw_message
        )
    
    def _parse_error_message(self, data: str, raw_message: str) -> ParsedMessage:
        """Parse an error message"""
        return ParsedMessage(
            message_type=MessageType.ERROR,
            data={"error": data},
            raw_content=raw_message
        )
    
    def _extract_battle_id(self, line: str) -> Optional[str]:
        """Extract battle ID from a line"""
        # Battle ID is usually in the format |init|battle|BATTLE_ID
        parts = line.split('|')
        if len(parts) >= 4 and parts[1] == 'init' and parts[2] == 'battle':
            return parts[3]
        return None
    
    def _parse_battle_event(self, line: str) -> Optional[Dict[str, Any]]:
        """Parse a single battle event line"""
        if not line.startswith('|'):
            return None
        
        parts = line[1:].split('|')
        if not parts:
            return None
        
        event_type = parts[0]
        event_data = parts[1:] if len(parts) > 1 else []
        
        # Parse common event types
        if event_type == 'player':
            return self._parse_player_event(event_data)
        elif event_type == 'teamsize':
            return self._parse_teamsize_event(event_data)
        elif event_type == 'start':
            return self._parse_start_event(event_data)
        elif event_type == 'switch' or event_type == 'drag':
            return self._parse_switch_event(event_type, event_data)
        elif event_type == 'move':
            return self._parse_move_event(event_data)
        elif event_type == 'damage' or event_type == 'heal':
            return self._parse_damage_heal_event(event_type, event_data)
        elif event_type == 'faint':
            return self._parse_faint_event(event_data)
        elif event_type == 'win':
            return self._parse_win_event(event_data)
        elif event_type == 'turn':
            return self._parse_turn_event(event_data)
        else:
            return {
                "type": event_type,
                "data": event_data,
                "raw": line
            }
    
    def _parse_player_event(self, data: List[str]) -> Dict[str, Any]:
        """Parse player event"""
        return {
            "type": "player",
            "player_id": data[0] if data else None,
            "username": data[1] if len(data) > 1 else None,
            "avatar": data[2] if len(data) > 2 else None
        }
    
    def _parse_teamsize_event(self, data: List[str]) -> Dict[str, Any]:
        """Parse teamsize event"""
        return {
            "type": "teamsize",
            "player_id": data[0] if data else None,
            "size": int(data[1]) if len(data) > 1 and data[1].isdigit() else None
        }
    
    def _parse_start_event(self, data: List[str]) -> Dict[str, Any]:
        """Parse start event"""
        return {
            "type": "start"
        }
    
    def _parse_switch_event(self, event_type: str, data: List[str]) -> Dict[str, Any]:
        """Parse switch/drag event"""
        pokemon_info = data[1].split(', ') if len(data) > 1 else []
        return {
            "type": event_type,
            "pokemon": data[0] if data else None,
            "species": pokemon_info[0] if pokemon_info else None,
            "level": pokemon_info[1] if len(pokemon_info) > 1 else None,
            "gender": pokemon_info[2] if len(pokemon_info) > 2 else None,
            "hp": data[2] if len(data) > 2 else None
        }
    
    def _parse_move_event(self, data: List[str]) -> Dict[str, Any]:
        """Parse move event"""
        return {
            "type": "move",
            "pokemon": data[0] if data else None,
            "move": data[1] if len(data) > 1 else None,
            "target": data[2] if len(data) > 2 else None
        }
    
    def _parse_damage_heal_event(self, event_type: str, data: List[str]) -> Dict[str, Any]:
        """Parse damage/heal event"""
        return {
            "type": event_type,
            "pokemon": data[0] if data else None,
            "hp": data[1] if len(data) > 1 else None,
            "source": data[2] if len(data) > 2 else None
        }
    
    def _parse_faint_event(self, data: List[str]) -> Dict[str, Any]:
        """Parse faint event"""
        return {
            "type": "faint",
            "pokemon": data[0] if data else None
        }
    
    def _parse_win_event(self, data: List[str]) -> Dict[str, Any]:
        """Parse win event"""
        return {
            "type": "win",
            "winner": data[0] if data else None
        }
    
    def _parse_turn_event(self, data: List[str]) -> Dict[str, Any]:
        """Parse turn event"""
        return {
            "type": "turn",
            "turn_number": int(data[0]) if data and data[0].isdigit() else None
        }
