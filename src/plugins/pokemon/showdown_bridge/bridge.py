"""
Pokemon Showdown Bridge

Main bridge class that coordinates all Pokemon Showdown interactions.
This is the primary interface that other parts of the plugin use.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any

from .process_manager import ShowdownProcessManager
from .battle_interface import ShowdownBattleInterface
from .message_parser import ShowdownMessageParser
from ..models import Battle, BattleFormat, BattlePlayer, Team
from ..config import config

from nonebot import logger


class ShowdownBridge:
    """Main bridge to Pokemon Showdown simulator"""
    
    def __init__(self):
        self.process_manager = ShowdownProcessManager()
        self.battle_interface = ShowdownBattleInterface(self.process_manager)
        self.message_parser = ShowdownMessageParser()
        self._initialized = False
        self._shutdown = False
    
    async def initialize(self) -> bool:
        """Initialize the Pokemon Showdown bridge"""
        if self._initialized:
            return True
        
        try:
            logger.info("Initializing Pokemon Showdown bridge...")
            
            # Verify Pokemon Showdown installation
            if not await self._verify_showdown_installation():
                logger.error("Pokemon Showdown installation verification failed")
                return False
            
            # Pre-start one process for faster battle creation
            process = await self.process_manager.get_available_process()
            if not process:
                logger.error("Failed to start initial Pokemon Showdown process")
                return False
            
            self._initialized = True
            logger.info("Pokemon Showdown bridge initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing Pokemon Showdown bridge: {e}")
            return False
    
    async def _verify_showdown_installation(self) -> bool:
        """Verify that Pokemon Showdown is properly installed and built"""
        try:
            import os
            from pathlib import Path
            
            showdown_path = Path(config.showdown_path)
            
            # Check if directory exists
            if not showdown_path.exists():
                logger.error(f"Pokemon Showdown directory not found: {showdown_path}")
                return False
            
            # Check if main script exists
            main_script = showdown_path / "pokemon-showdown"
            if not main_script.exists():
                logger.error(f"Pokemon Showdown main script not found: {main_script}")
                return False
            
            # Check if dist directory exists (built files)
            dist_dir = showdown_path / "dist"
            if not dist_dir.exists():
                logger.error(f"Pokemon Showdown dist directory not found. Run 'npm run build' in {showdown_path}")
                return False
            
            # Check if simulator files exist
            sim_files = [
                "dist/sim/index.js",
                "dist/sim/battle.js",
                "dist/sim/dex.js"
            ]
            
            for file_path in sim_files:
                full_path = showdown_path / file_path
                if not full_path.exists():
                    logger.error(f"Required Pokemon Showdown file not found: {full_path}")
                    return False
            
            logger.info("Pokemon Showdown installation verified")
            return True
            
        except Exception as e:
            logger.error(f"Error verifying Pokemon Showdown installation: {e}")
            return False
    
    async def create_battle(
        self, 
        format: BattleFormat, 
        player1_team: Team, 
        player1_username: str,
        player2_team: Team, 
        player2_username: str
    ) -> Optional[Battle]:
        """Create a new battle"""
        if not self._initialized:
            logger.error("Bridge not initialized")
            return None
        
        if self._shutdown:
            logger.error("Bridge is shutting down")
            return None
        
        try:
            # Create battle players
            player1 = BattlePlayer(
                user_id="p1",
                username=player1_username,
                team=player1_team
            )
            
            player2 = BattlePlayer(
                user_id="p2", 
                username=player2_username,
                team=player2_team
            )
            
            # Create battle through interface
            battle = await self.battle_interface.create_battle(format, player1, player2)
            
            if battle:
                logger.info(f"Created battle {battle.id} between {player1_username} and {player2_username}")
            
            return battle
            
        except Exception as e:
            logger.error(f"Error creating battle: {e}")
            return None
    
    async def send_battle_action(self, battle_id: str, player_id: str, action: str) -> bool:
        """Send a battle action"""
        if not self._initialized:
            logger.error("Bridge not initialized")
            return False
        
        return await self.battle_interface.send_battle_action(battle_id, player_id, action)
    
    def get_battle(self, battle_id: str) -> Optional[Battle]:
        """Get a battle by ID"""
        return self.battle_interface.get_battle(battle_id)
    
    def list_active_battles(self) -> List[Battle]:
        """List all active battles"""
        return self.battle_interface.list_active_battles()
    
    def add_battle_callback(self, battle_id: str, callback: Callable):
        """Add a callback for battle events"""
        self.battle_interface.add_battle_callback(battle_id, callback)
    
    def remove_battle_callback(self, battle_id: str, callback: Callable):
        """Remove a battle callback"""
        self.battle_interface.remove_battle_callback(battle_id, callback)
    
    async def validate_team(self, team: Team, format: BattleFormat) -> tuple[bool, List[str]]:
        """Validate a team for a specific format"""
        try:
            # Create a temporary process for validation
            process = await self.process_manager.get_available_process()
            if not process:
                return False, ["No available process for validation"]
            
            # Use Pokemon Showdown's team validator
            # This would require implementing a validation interface
            # For now, return basic validation
            
            errors = []
            
            # Basic validation
            if len(team.pokemon) == 0:
                errors.append("Team cannot be empty")
            
            if len(team.pokemon) > 6:
                errors.append("Team cannot have more than 6 Pokemon")
            
            for i, pokemon in enumerate(team.pokemon):
                if not pokemon.species:
                    errors.append(f"Pokemon {i+1} must have a species")
                
                if len(pokemon.moves) == 0:
                    errors.append(f"Pokemon {i+1} ({pokemon.species}) must have at least one move")
                
                if len(pokemon.moves) > 4:
                    errors.append(f"Pokemon {i+1} ({pokemon.species}) cannot have more than 4 moves")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            logger.error(f"Error validating team: {e}")
            return False, [f"Validation error: {str(e)}"]
    
    async def get_format_info(self, format: BattleFormat) -> Optional[Dict[str, Any]]:
        """Get information about a battle format"""
        try:
            # This would query Pokemon Showdown for format information
            # For now, return basic format info
            
            format_info = {
                "id": format.value,
                "name": format.value.upper(),
                "team_size": 6,
                "level_cap": 50 if "lc" in format.value else 100,
                "description": f"Generation 9 {format.value.replace('gen9', '').upper()} format"
            }
            
            return format_info
            
        except Exception as e:
            logger.error(f"Error getting format info: {e}")
            return None
    
    async def shutdown(self):
        """Shutdown the Pokemon Showdown bridge"""
        if self._shutdown:
            return
        
        logger.info("Shutting down Pokemon Showdown bridge...")
        self._shutdown = True
        
        try:
            # Shutdown all processes
            await self.process_manager.shutdown_all()
            
            logger.info("Pokemon Showdown bridge shut down successfully")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    @property
    def is_initialized(self) -> bool:
        """Check if the bridge is initialized"""
        return self._initialized
    
    @property
    def is_shutdown(self) -> bool:
        """Check if the bridge is shut down"""
        return self._shutdown
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check of the bridge"""
        try:
            health = {
                "initialized": self._initialized,
                "shutdown": self._shutdown,
                "active_processes": len(self.process_manager.processes),
                "active_battles": len(self.battle_interface.active_battles),
                "showdown_path_exists": await self._verify_showdown_installation()
            }
            
            return health
            
        except Exception as e:
            logger.error(f"Error during health check: {e}")
            return {
                "initialized": False,
                "shutdown": True,
                "error": str(e)
            }
